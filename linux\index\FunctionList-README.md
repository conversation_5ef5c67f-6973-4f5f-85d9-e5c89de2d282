# FunctionList 功能列表组件

基于 Vue 3 Composition API 和 uvui 组件库开发的横向功能列表组件，支持 4-5 列自适应布局。

## 功能特性

- ✅ 支持 4-5 列横向布局
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 集成 uvui 图标组件
- ✅ 支持点击事件处理
- ✅ 支持禁用状态和角标提示
- ✅ 自定义图标颜色和大小
- ✅ 遵循项目设计规范

## 基础用法

```vue
<template>
  <function-list 
    title="插件"
    :function-list="functionData"
    :columns="5"
    @itemClick="handleItemClick"
  />
</template>

<script setup>
import FunctionList from './functionList.vue'

const functionData = ref([
  {
    id: 1,
    icon: 'monitor',
    label: '站点监控',
    iconColor: '#20a50a'
  },
  {
    id: 2,
    icon: 'plus-circle', 
    label: '系统加固',
    iconColor: '#20a50a'
  }
])

const handleItemClick = ({ item, index }) => {
  console.log('点击了:', item.label)
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| functionList | Array | [] | 功能列表数据 |
| title | String | '' | 列表标题 |
| columns | Number | 5 | 每行显示列数 (4-5) |
| responsive | Boolean | true | 是否启用响应式布局 |
| customStyle | Object/String | {} | 自定义样式 |
| customClass | String | '' | 自定义类名 |

## 功能项数据结构

```javascript
{
  id: 1,                    // 唯一标识
  icon: 'monitor',          // 图标名称 (uvui图标)
  label: '站点监控',         // 显示文字
  iconColor: '#20a50a',     // 图标颜色 (可选)
  iconSize: '32',           // 图标大小 (可选)
  iconStyle: {},            // 图标自定义样式 (可选)
  disabled: false,          // 是否禁用 (可选)
  badge: 'NEW',             // 角标文字 (可选)
  onClick: function() {}    // 自定义点击处理 (可选)
}
```

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| itemClick | 点击功能项时触发 | { item, index } |
| click | 点击功能项时触发 (别名) | { item, index } |

## 响应式布局

组件默认启用响应式布局：

- 大屏幕 (>750rpx): 按设置的 columns 显示
- 中等屏幕 (≤750rpx): 显示 4 列
- 小屏幕 (≤600rpx): 显示 3 列

## 样式定制

### 主题色适配

组件自动使用项目主题色 `#20a50a`，也可以为每个功能项单独设置颜色：

```javascript
{
  icon: 'monitor',
  label: '监控',
  iconColor: '#3c9cff'  // 自定义颜色
}
```

### 自定义样式

```vue
<function-list 
  :function-list="data"
  :custom-style="{ padding: '32rpx' }"
  custom-class="my-function-list"
/>
```

## 使用示例

参考 `functionListExample.vue` 文件查看完整的使用示例，包括：

- 基础 5 列布局
- 4 列布局
- 带角标和禁用状态
- 自定义图标样式

## 注意事项

1. 确保项目已安装 uvui 组件库
2. 功能项的 `icon` 属性需要使用 uvui 支持的图标名称
3. 建议为每个功能项设置唯一的 `id` 值
4. 响应式布局在小屏幕设备上会自动调整列数
5. 禁用状态的功能项不会触发点击事件

## 兼容性

- Vue 3.x
- uni-app
- uvui 组件库
- 支持 H5、小程序、App 等多端
