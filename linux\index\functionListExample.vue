<template>
	<page-container>
		<template #title>
			<view class="custom-title-container">
				<view class="title-main text-secondary">功能列表示例</view>
				<view class="title-subtitle">
					<uv-icon name="info-circle" size="14" color="#999"></uv-icon>
					<text class="subtitle-text">演示功能列表组件的使用方法</text>
				</view>
			</view>
		</template>

		<view class="example-container">
			<!-- 基础用法 -->
			<view class="example-section">
				<view class="section-title">基础用法 (5列布局)</view>
				<function-list 
					title="插件"
					:function-list="basicFunctionList"
					:columns="5"
					@itemClick="handleItemClick"
				/>
			</view>

			<!-- 4列布局 -->
			<view class="example-section">
				<view class="section-title">4列布局</view>
				<function-list 
					:function-list="fourColumnList"
					:columns="4"
					@itemClick="handleItemClick"
				/>
			</view>

			<!-- 带角标和禁用状态 -->
			<view class="example-section">
				<view class="section-title">高级功能</view>
				<function-list 
					title="系统工具"
					:function-list="advancedFunctionList"
					:columns="5"
					@itemClick="handleItemClick"
				/>
			</view>

			<!-- 自定义图标颜色 -->
			<view class="example-section">
				<view class="section-title">自定义样式</view>
				<function-list 
					:function-list="customStyleList"
					:columns="4"
					@itemClick="handleItemClick"
				/>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue'
	import FunctionList from './functionList.vue'

	// 基础功能列表数据
	const basicFunctionList = ref([
		{
			id: 1,
			icon: 'monitor',
			label: '站点监控',
			iconColor: '#20a50a'
		},
		{
			id: 2,
			icon: 'plus-circle',
			label: '系统加固',
			iconColor: '#20a50a'
		},
		{
			id: 3,
			icon: 'server',
			label: 'nginx防火墙',
			iconColor: '#20a50a'
		},
		{
			id: 4,
			icon: 'shield',
			label: '防篡改',
			iconColor: '#20a50a'
		},
		{
			id: 5,
			icon: 'setting',
			label: '设置',
			iconColor: '#20a50a'
		}
	])

	// 4列布局数据
	const fourColumnList = ref([
		{
			id: 1,
			icon: 'file-text',
			label: '文件管理',
			iconColor: '#3c9cff'
		},
		{
			id: 2,
			icon: 'database',
			label: '数据库',
			iconColor: '#3c9cff'
		},
		{
			id: 3,
			icon: 'terminal',
			label: '终端',
			iconColor: '#3c9cff'
		},
		{
			id: 4,
			icon: 'clock',
			label: '计划任务',
			iconColor: '#3c9cff'
		}
	])

	// 高级功能列表（带角标和禁用状态）
	const advancedFunctionList = ref([
		{
			id: 1,
			icon: 'cpu',
			label: 'CPU监控',
			iconColor: '#f9ae3d',
			badge: 'NEW'
		},
		{
			id: 2,
			icon: 'memory',
			label: '内存监控',
			iconColor: '#f9ae3d'
		},
		{
			id: 3,
			icon: 'hard-drive',
			label: '磁盘监控',
			iconColor: '#f9ae3d'
		},
		{
			id: 4,
			icon: 'network',
			label: '网络监控',
			iconColor: '#c0c4cc',
			disabled: true
		},
		{
			id: 5,
			icon: 'log',
			label: '日志分析',
			iconColor: '#f9ae3d',
			badge: '5'
		}
	])

	// 自定义样式列表
	const customStyleList = ref([
		{
			id: 1,
			icon: 'backup',
			label: '备份管理',
			iconColor: '#20a50a',
			iconSize: '36'
		},
		{
			id: 2,
			icon: 'security',
			label: '安全中心',
			iconColor: '#f56c6c',
			iconSize: '36'
		},
		{
			id: 3,
			icon: 'update',
			label: '系统更新',
			iconColor: '#909399',
			iconSize: '36'
		},
		{
			id: 4,
			icon: 'help',
			label: '帮助文档',
			iconColor: '#3c9cff',
			iconSize: '36'
		}
	])

	// 处理项目点击事件
	const handleItemClick = ({ item, index }) => {
		console.log('点击了功能项:', item.label, '索引:', index)
		
		// 这里可以根据不同的功能项执行不同的操作
		switch (item.id) {
			case 1:
				uni.showToast({
					title: `点击了: ${item.label}`,
					icon: 'none'
				})
				break
			default:
				uni.showToast({
					title: `功能 "${item.label}" 开发中...`,
					icon: 'none'
				})
		}
	}
</script>

<style lang="scss" scoped>
	.example-container {
		padding: 32rpx;
	}

	.example-section {
		margin-bottom: 64rpx;

		&:last-child {
			margin-bottom: 32rpx;
		}
	}

	.section-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #303133;
		margin-bottom: 24rpx;
		padding: 0 16rpx;
	}

	.custom-title-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;

		.title-main {
			font-size: 36rpx;
			font-weight: 600;
			color: #303133;
		}

		.title-subtitle {
			display: flex;
			align-items: center;
			gap: 8rpx;

			.subtitle-text {
				font-size: 24rpx;
				color: #909399;
			}
		}
	}
</style>
